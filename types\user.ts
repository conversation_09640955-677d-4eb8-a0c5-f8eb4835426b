export interface User {
  id: string
  first_name: string
  last_name: string
  email: string
  updated_at_platform: string
  created_at_platform: string
  // Optional fields from full user response
  groups?: any[]
  user_permissions?: any[]
  last_login?: string | null
  is_superuser?: boolean
  is_staff?: boolean
  is_active?: boolean
  date_joined?: string
}

export interface CreateUserRequest {
  first_name: string
  last_name: string
  email: string
  role_id: number
  password: string
}

export interface UpdateUserRequest {
  group_id: number
}

// For the UI component compatibility
export interface UIUser {
  id: string
  name: string
  email: string
  role: string
  status: "active" | "inactive"
  created_at: string
  updated_at?: string
}

export interface UserSessionInfo {
  user: User
  session: {
    accessToken: string
    refreshToken: string
    token_created_at: string
    access_token_expires_at: string
    refresh_token_expires_at: string
    token_type: string
  }
}

// Helper function to convert API User to UI User
export function convertToUIUser(apiUser: User): UIUser {
  return {
    id: apiUser.id,
    name: `${apiUser.first_name} ${apiUser.last_name}`.trim() || 'N/A',
    email: apiUser.email,
    role: (apiUser.groups && apiUser.groups.length > 0) ? apiUser.groups[0].name : 'N/A',
    status: apiUser.is_active !== false ? 'active' : 'inactive',
    created_at: apiUser.created_at_platform || apiUser.date_joined || new Date().toISOString(),
    updated_at: apiUser.updated_at_platform
  }
}

// Helper function to convert UI User to API format for creation
export function convertToCreateUserRequest(uiUser: Omit<UIUser, "id" | "created_at">, roleId: number = 1, password: string = 'defaultPassword'): CreateUserRequest {
  const nameParts = uiUser.name.split(' ')
  return {
    first_name: nameParts[0] || '',
    last_name: nameParts.slice(1).join(' ') || '',
    email: uiUser.email,
    role_id: roleId,
    password: password
  }
}
