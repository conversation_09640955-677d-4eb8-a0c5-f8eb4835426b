"use client"

import React, { useState } from 'react'
import { SalesforceFolder } from '@/types/salesforce'
import { useSalesforcefolders } from '@/hooks/use-salesforce-folders'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Spinner } from '@/components/ui/shadcn-io/spinner'
import { FolderTree } from './folder-tree'
import { CreateFolderDialog } from './create-folder-dialog'
import { Folder, Search, Plus, RefreshCw } from 'lucide-react'
import { cn } from '@/lib/utils'

interface FolderSelectorProps {
  selectedFolderId?: string
  onFolderSelect: (folder: SalesforceFolder | null) => void
  className?: string
}

export const FolderSelector: React.FC<FolderSelectorProps> = ({
  selectedFolderId,
  onFolderSelect,
  className
}) => {
  const { folders, flatFolders, loading, error, refetch } = useSalesforcefolders()
  const [searchQuery, setSearchQuery] = useState('')
  const [showCreateDialog, setShowCreateDialog] = useState(false)
  const [parentFolderForCreation, setParentFolderForCreation] = useState<SalesforceFolder | null>(null)

  // Filter folders based on search query
  const filteredFolders = React.useMemo(() => {
    if (!searchQuery.trim()) {
      return folders
    }

    const query = searchQuery.toLowerCase()
    const matchingFolderIds = new Set(
      flatFolders
        .filter(folder => 
          folder.name.toLowerCase().includes(query) ||
          folder.description.toLowerCase().includes(query) ||
          folder.full_path.toLowerCase().includes(query)
        )
        .map(folder => folder.id)
    )

    // Filter the tree structure to only show matching folders and their parents
    const filterTree = (folders: SalesforceFolder[]): SalesforceFolder[] => {
      return folders.map(folder => {
        const hasMatchingChildren = folder.children ? filterTree(folder.children).length > 0 : false
        const isMatch = matchingFolderIds.has(folder.id)
        
        if (isMatch || hasMatchingChildren) {
          return {
            ...folder,
            children: folder.children ? filterTree(folder.children) : undefined
          }
        }
        return null
      }).filter(Boolean) as SalesforceFolder[]
    }

    return filterTree(folders)
  }, [folders, flatFolders, searchQuery])

  const handleFolderSelect = (folder: SalesforceFolder) => {
    // Radio button behavior: always select the new folder
    // The parent component will handle deselecting any previously selected folder
    onFolderSelect(folder)
  }

  const handleCreateFolder = (parentFolder: SalesforceFolder) => {
    setParentFolderForCreation(parentFolder)
    setShowCreateDialog(true)
  }

  const handleFolderCreated = (newFolder: SalesforceFolder) => {
    // Refresh the folder list to include the new folder
    refetch()
    // Optionally select the newly created folder
    onFolderSelect(newFolder)
  }

  const handleClearSelection = () => {
    onFolderSelect(null)
  }

  const selectedFolder = selectedFolderId 
    ? flatFolders.find(f => f.id === selectedFolderId)
    : null

  if (loading) {
    return (
      <Card className={className}>
        <CardContent className="p-6">
          <div className="flex items-center justify-center py-12">
            <Spinner variant="infinite" size={64} />
          </div>
        </CardContent>
      </Card>
    )
  }

  if (error) {
    return (
      <Card className={className}>
        <CardHeader>
          <CardTitle className="flex items-center gap-2 text-destructive">
            <Folder className="h-5 w-5" />
            Error al carregar les carpetes
          </CardTitle>
          <CardDescription>
            {error}
          </CardDescription>
        </CardHeader>
        <CardContent>
          <Button onClick={refetch} variant="outline" className="w-full">
            <RefreshCw className="h-4 w-4 mr-2" />
            Tornar a intentar
          </Button>
        </CardContent>
      </Card>
    )
  }

  return (
    <>
      <Card className={className}>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Folder className="h-5 w-5" />
            Carpeta de Salesforce
          </CardTitle>
          <CardDescription>
            Selecciona una carpeta per organitzar la newsletter a Salesforce
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          {/* Search */}
          <div className="relative">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
            <Input
              placeholder="Cercar carpetes..."
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
              className="pl-10"
            />
          </div>

          {/* Selected folder display */}
          {selectedFolder && (
            <div className="p-3 bg-blue-100 border border-blue-300 rounded-md">
              <div className="flex items-center justify-between">
                <div className="min-w-0 flex-1">
                  <p className="text-sm font-medium text-blue-900 truncate">{selectedFolder.name}</p>
                  <p className="text-xs text-blue-700 truncate">
                    {selectedFolder.full_path || selectedFolder.description}
                  </p>
                </div>
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={handleClearSelection}
                  className="ml-2 text-blue-700 hover:text-blue-900 hover:bg-blue-200"
                >
                  Deseleccionar
                </Button>
              </div>
            </div>
          )}

          {/* Folder tree */}
          <div className="max-h-96 overflow-y-auto border rounded-md p-2">
            {filteredFolders.length > 0 ? (
              <FolderTree
                folders={filteredFolders}
                selectedFolderId={selectedFolderId}
                onFolderSelect={handleFolderSelect}
                onCreateFolder={handleCreateFolder}
              />
            ) : (
              <div className="text-center py-8 text-muted-foreground">
                {searchQuery ? 'No s\'han trobat carpetes que coincideixin amb la cerca' : 'No hi ha carpetes disponibles'}
              </div>
            )}
          </div>

          {/* Actions */}
          <div className="flex gap-2">
            <Button
              variant="outline"
              size="sm"
              onClick={refetch}
              className="flex-1"
            >
              <RefreshCw className="h-4 w-4 mr-2" />
              Actualitzar
            </Button>
          </div>
        </CardContent>
      </Card>

      {/* Create folder dialog */}
      <CreateFolderDialog
        open={showCreateDialog}
        onOpenChange={setShowCreateDialog}
        parentFolder={parentFolderForCreation}
        onFolderCreated={handleFolderCreated}
      />
    </>
  )
}
