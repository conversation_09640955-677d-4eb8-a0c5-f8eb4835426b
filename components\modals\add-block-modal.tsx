"use client"

import * as React from "react"
import { useState } from "react"
import { But<PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { Separator } from "@/components/ui/separator"
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog"
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table"
import { Search, Plus, Loader2, AlertCircle } from "lucide-react"
import { cn } from "@/lib/utils"
import { Block } from "@/types/block"
import { useAvailableBlocks } from "@/hooks/use-available-blocks"
import { useDebounce } from "@/hooks/use-debounce"

interface AddBlockModalProps {
  open: boolean
  onOpenChange: (open: boolean) => void
  brand?: string
  onBlockSelect: (block: Block) => void
}

export function AddBlockModal({
  open,
  onOpenChange,
  brand,
  onBlockSelect
}: AddBlockModalProps) {
  const [search, setSearch] = useState("")
  const debouncedSearch = useDebounce(search, 300)

  const {
    blocks,
    loading,
    error,
    refetch
  } = useAvailableBlocks({
    brand,
    search: debouncedSearch || undefined,
    isActive: true
  })

  const handleBlockSelect = (block: Block) => {
    onBlockSelect(block)
    onOpenChange(false)
    setSearch("") // Reset search when closing
  }

  const handleClose = () => {
    onOpenChange(false)
    setSearch("") // Reset search when closing
  }

  return (
    <Dialog open={open} onOpenChange={handleClose}>
      <DialogContent className="max-w-4xl max-h-[80vh] overflow-hidden flex flex-col">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <Plus className="h-5 w-5" />
            Add Block to Newsletter
          </DialogTitle>
          <DialogDescription>
            Select a block to add to your newsletter. Only blocks from the same brand are shown.
          </DialogDescription>
        </DialogHeader>

        <div className="flex-1 overflow-hidden flex flex-col space-y-4 overflow-y-auto">
          {/* Search */}
          <div className="space-y-2">
            <Label htmlFor="search">Search blocks</Label>
            <div className="relative">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
              <Input
                id="search"
                placeholder="Search by name or description..."
                value={search}
                onChange={(e) => setSearch(e.target.value)}
                className="pl-10"
              />
            </div>
          </div>

          {/* Content */}
          <div className="flex-1 overflow-hidden">
            {loading ? (
              <div className="flex items-center justify-center h-32">
                <Loader2 className="h-6 w-6 animate-spin" />
                <span className="ml-2">Loading blocks...</span>
              </div>
            ) : error ? (
              <div className="flex items-center justify-center h-32 text-destructive">
                <AlertCircle className="h-6 w-6" />
                <span className="ml-2">{error}</span>
                <Button variant="outline" size="sm" onClick={refetch} className="ml-4">
                  Retry
                </Button>
              </div>
            ) : blocks.length === 0 ? (
              <div className="flex items-center justify-center h-32 text-muted-foreground">
                <span>No blocks found{search ? ` for "${search}"` : ""}</span>
              </div>
            ) : (
              <div className="border rounded-lg max-h-96 overflow-y-auto">
                <Table>
                  <TableHeader className="sticky top-0 bg-background">
                    <TableRow>
                      <TableHead className="w-[40%]">Name</TableHead>
                      <TableHead className="w-[40%]">Description</TableHead>
                      <TableHead className="w-[20%] text-right">Action</TableHead>
                    </TableRow>
                  </TableHeader>
                  <TableBody>
                    {blocks.map((block) => (
                      <TableRow key={block.id} className="hover:bg-muted/50">
                        <TableCell>
                          <div className="space-y-1">
                            <div className="font-medium">{block.name}</div>
                            <Badge variant="outline" className="text-xs">
                              {block.brand_name}
                            </Badge>
                          </div>
                        </TableCell>
                        <TableCell>
                          <div className="text-sm text-muted-foreground">
                            {block.description || "No description"}
                          </div>
                        </TableCell>
                        <TableCell className="text-right">
                          <Button
                            size="sm"
                            onClick={() => handleBlockSelect(block)}
                            className="w-full sm:w-auto"
                          >
                            Add Block
                          </Button>
                        </TableCell>
                      </TableRow>
                    ))}
                  </TableBody>
                </Table>
              </div>
            )}
          </div>
        </div>
      </DialogContent>
    </Dialog>
  )
}
