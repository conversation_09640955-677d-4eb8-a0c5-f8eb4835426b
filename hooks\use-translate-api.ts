import { useState, useCallback } from 'react'
import { useSession } from 'next-auth/react'
import { NewsletterBuilderData } from '@/types/newsletter'
import { DEFAULT_API_URL } from '@/constants/constants'
import { extractApiErrorMessage } from '@/lib/api-error-utils'

interface TranslateRequest {
  language: string // This is now the base language
  newsletter_id: string
  prompt: string // This is now the context
}

interface TranslateResponse {
  newsletter: NewsletterBuilderData
  language: string
  originalPrompt: string
}

interface UseTranslateApiReturn {
  translateNewsletter: (request: TranslateRequest) => Promise<TranslateResponse>
  loading: boolean
  error: string | null
}

const API_BASE_URL = process.env.NEXT_PUBLIC_BACKEND_URL || DEFAULT_API_URL

export function useTranslateApi(): UseTranslateApiReturn {
  const { data: session } = useSession()
  const [loading, setLoading] = useState(false)
  const [error, setError] = useState<string | null>(null)

  const getAuthHeaders = useCallback(() => {
    const headers: Record<string, string> = {
      'Content-Type': 'application/json',
    }

    if (session?.djangoAccessToken) {
      headers['Authorization'] = `Bearer ${session.djangoAccessToken}`
    }

    return headers
  }, [session?.djangoAccessToken])

  const translateNewsletter = useCallback(async (request: TranslateRequest): Promise<TranslateResponse> => {
    if (!session?.djangoAccessToken) {
      throw new Error('No authentication token available')
    }

    setLoading(true)
    setError(null)

    try {
      // For testing purposes, simulate the API response by calling the existing GET endpoint
      // TODO: Replace with actual translation API endpoint when available
      
      // Simulate network delay
      await new Promise(resolve => setTimeout(resolve, 2000 + Math.random() * 1000))

      // Fetch the current newsletter data to simulate translation
      const response = await fetch(`${API_BASE_URL}/builder/newsletter-blocks?newsletter_parent_id=${request.newsletter_id}`, {
        method: 'GET',
        headers: getAuthHeaders(),
      })

      if (!response.ok) {
        const errorMessage = await extractApiErrorMessage(response, 'Failed to fetch newsletter for translation')
        throw new Error(errorMessage)
      }

      const newsletterData: NewsletterBuilderData = await response.json()

      // Simulate translation by modifying content based on base language and context
      const translatedNewsletter = simulateTranslation(newsletterData, request.language, request.prompt)

      const translationResponse: TranslateResponse = {
        newsletter: translatedNewsletter,
        language: request.language,
        originalPrompt: request.prompt
      }

      return translationResponse

      // TODO: Uncomment and implement when actual translation API is available
      /*
      const response = await fetch(`${API_BASE_URL}/openai/traduce-newsletter/`, {
        method: 'POST',
        headers: getAuthHeaders(),
        body: JSON.stringify(request)
      })

      if (!response.ok) {
        const errorMessage = await extractApiErrorMessage(response, 'Failed to translate newsletter')
        throw new Error(errorMessage)
      }

      const data: TranslateResponse = await response.json()
      return data
      */
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'An error occurred while translating newsletter'
      setError(errorMessage)
      throw err
    } finally {
      setLoading(false)
    }
  }, [session?.djangoAccessToken, getAuthHeaders])

  return {
    translateNewsletter,
    loading,
    error
  }
}

// Helper function to simulate translation for testing
function simulateTranslation(newsletter: NewsletterBuilderData, baseLanguage: string, context: string): NewsletterBuilderData {
  const languageMap: Record<string, string> = {
    'es': 'Español',
    'ca': 'Català',
    'fr': 'Français',
    'en': 'English'
  }

  const translatedNewsletter = JSON.parse(JSON.stringify(newsletter)) // Deep clone

  // Add a prefix to the newsletter name to indicate it's translated from base language
  translatedNewsletter.name = `[FROM ${languageMap[baseLanguage] || baseLanguage.toUpperCase()}] ${newsletter.name}`

  // Simulate translation of block content
  translatedNewsletter.nl_blocks = translatedNewsletter.nl_blocks.map(block => ({
    ...block,
    name: `${block.name} (Translated from ${languageMap[baseLanguage] || baseLanguage.toUpperCase()})`,
    html_content: block.html_content ? `<!-- Translated from ${baseLanguage} with context: "${context}" -->\n${block.html_content}\n<!-- Translation completed -->` : block.html_content,
    variable_values: block.variable_values?.map(variable => ({
      ...variable,
      value: Object.fromEntries(
        Object.entries(variable.value).map(([lang, value]) => [
          lang,
          `[TRANSLATED FROM ${baseLanguage.toUpperCase()}] ${value}`
        ])
      )
    }))
  }))

  // Simulate translation of headers
  translatedNewsletter.headers = translatedNewsletter.headers.map(header => ({
    ...header,
    name: `${header.name} (Translated from ${languageMap[baseLanguage] || baseLanguage.toUpperCase()})`,
    html_content: header.html_content ? `<!-- Translated from ${baseLanguage} with context: "${context}" -->\n${header.html_content}\n<!-- Translation completed -->` : header.html_content,
    variable_values: header.variable_values?.map(variable => ({
      ...variable,
      value: Object.fromEntries(
        Object.entries(variable.value).map(([lang, value]) => [
          lang,
          `[TRANSLATED FROM ${baseLanguage.toUpperCase()}] ${value}`
        ])
      )
    }))
  }))

  // Simulate translation of footers
  translatedNewsletter.footers = translatedNewsletter.footers.map(footer => ({
    ...footer,
    name: `${footer.name} (Translated from ${languageMap[baseLanguage] || baseLanguage.toUpperCase()})`,
    html_content: footer.html_content ? `<!-- Translated from ${baseLanguage} with context: "${context}" -->\n${footer.html_content}\n<!-- Translation completed -->` : footer.html_content,
    variable_values: footer.variable_values?.map(variable => ({
      ...variable,
      value: Object.fromEntries(
        Object.entries(variable.value).map(([lang, value]) => [
          lang,
          `[TRANSLATED FROM ${baseLanguage.toUpperCase()}] ${value}`
        ])
      )
    }))
  }))

  return translatedNewsletter
}
