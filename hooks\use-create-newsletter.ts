import { useState, useCallback } from 'react'
import { useSession } from 'next-auth/react'
import { CreateNewsletterRequest, CreateNewsletterResponse } from '@/types/newsletter'
import { extractApiErrorMessage } from '@/lib/api-error-utils'
import { DEFAULT_API_URL } from '@/constants/constants'

interface UseCreateNewsletterReturn {
  createNewsletter: (data: CreateNewsletterRequest) => Promise<CreateNewsletterResponse>
  loading: boolean
  error: string | null
}

export function useCreateNewsletter(): UseCreateNewsletterReturn {
  const { data: session } = useSession()
  const [loading, setLoading] = useState(false)
  const [error, setError] = useState<string | null>(null)

  const createNewsletter = useCallback(async (data: CreateNewsletterRequest): Promise<CreateNewsletterResponse> => {
    if (!session?.djangoAccessToken) {
      throw new Error('Autenticació requerida')
    }

    setLoading(true)
    setError(null)

    try {
      const backendUrl = process.env.NEXT_PUBLIC_BACKEND_URL || DEFAULT_API_URL
      
      const response = await fetch(`${backendUrl}/newsletters/create/`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${session.djangoAccessToken}`
        },
        body: JSON.stringify(data)
      })

      if (!response.ok) {
        const errorMessage = await extractApiErrorMessage(response, 'Failed to create newsletter')
        throw new Error(errorMessage)
      }

      const result: CreateNewsletterResponse = await response.json()
      return result
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'An error occurred while creating the newsletter'
      setError(errorMessage)
      throw err
    } finally {
      setLoading(false)
    }
  }, [session?.djangoAccessToken])

  return {
    createNewsletter,
    loading,
    error
  }
}
