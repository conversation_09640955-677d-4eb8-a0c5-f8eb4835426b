"use client";

import { UserManagement } from "@/components/users/user-management";
import React, { useMemo, useState, useEffect } from "react";
import { toast } from "sonner";
import { UIUser, convertToUIUser, convertToCreateUserRequest } from "@/types/user";
import { useUsers } from "@/hooks/use-users";
import { useRoles } from "@/hooks/use-roles";
import { Spinner } from "@/components/ui/shadcn-io/spinner";

export default function Page() {
    // Pagination state
    const [page, setPage] = useState(1)
    const [pageSize, setPageSize] = useState(10)
    const [searchQuery, setSearchQuery] = useState("")
    const [debouncedSearch, setDebouncedSearch] = useState("")

    // Debounce search query
    useEffect(() => {
        const timer = setTimeout(() => {
            setDebouncedSearch(searchQuery)
        }, 300)

        return () => clearTimeout(timer)
    }, [searchQuery])

    // Reset page when search changes
    useEffect(() => {
        setPage(1)
    }, [debouncedSearch])

    const {
        users: apiUsers,
        loading: usersLoading,
        error: usersError,
        totalCount,
        hasNext,
        hasPrevious,
        refetch,
        createUser,
        updateUser,
        deleteUser
    } = useUsers({
        search: debouncedSearch || undefined,
        page,
        pageSize
    });

    const {
        roles,
        loading: rolesLoading,
        error: rolesError
    } = useRoles();

    const loading = usersLoading || rolesLoading;
    const error = usersError || rolesError;

    // Convert API users to UI users
    const users = useMemo(() => {
        return apiUsers.map(convertToUIUser);
    }, [apiUsers]);

    // Create user
    const handleCreateUser = async (user: Omit<UIUser, "id" | "created_at">) => {
        try {
            // Find the role ID from the fetched roles
            const selectedRole = roles.find(role => role.name === user.role)
            const roleId = selectedRole?.id || 1 // Default to 1 if role not found
            const createRequest = convertToCreateUserRequest(user, roleId, 'defaultPassword123')

            await createUser(createRequest);
            toast.success('User created successfully')
        } catch (err) {
            console.error('Error creating user:', err);
            toast.error('Failed to create user')
        }
    }

    // Update user
    const handleUpdateUser = async (id: string, user: Partial<UIUser>) => {
        try {
            // Find the role ID from the fetched roles if role is being updated
            let groupId = 1 // Default group
            if (user.role) {
                const selectedRole = roles.find(role => role.name === user.role)
                groupId = selectedRole?.id || 1
            }

            await updateUser(id, { group_id: groupId });
            toast.success('User updated successfully')
            refetch() // Refresh the current page
        } catch (err) {
            console.error('Error updating user:', err);
            toast.error('Failed to update user')
        }
    }

    // Delete user
    const handleDeleteUser = async (id: string) => {
        try {
            await deleteUser(id);
            toast.success('User deleted successfully')
            refetch() // Refresh the current page
        } catch (err) {
            console.error('Error deleting user:', err);
            toast.error('Failed to delete user')
        }
    }

    if (loading) {
        return (
            <div className="p-6 space-y-6">
                <div className="flex items-center justify-center h-64">
                    <div className="flex flex-col items-center gap-2 mb-4">
                        <div>
                            <Spinner key="infinite" variant="infinite" size={64} />
                        </div>
                        <div className="text-center animate-pulse">
                            Carregant usuaris...
                        </div>
                    </div>
                </div>
            </div>
        )
    }

    if (error) {
        return (
            <div className="p-6 space-y-6">
                <div className="flex items-center justify-center h-64">
                    <div className="text-lg text-red-600">Error: {error}</div>
                </div>
            </div>
        )
    }

  return (
    <div className="p-6 space-y-6">
      <div className="">
        <UserManagement
          users={users}
          roles={roles}
          onCreateUser={handleCreateUser}
          onUpdateUser={handleUpdateUser}
          onDeleteUser={handleDeleteUser}
          onRefresh={refetch}
          searchQuery={searchQuery}
          onSearchChange={setSearchQuery}
          pagination={{
            page,
            pageSize,
            totalCount,
            hasNext,
            hasPrevious,
            onPageChange: setPage,
            onPageSizeChange: setPageSize
          }}
        />
      </div>
    </div>
  )
}
