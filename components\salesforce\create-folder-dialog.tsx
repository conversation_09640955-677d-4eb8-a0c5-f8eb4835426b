"use client"

import React, { useState } from 'react'
import { SalesforceFolder } from '@/types/salesforce'
import { useCreateSalesforceFolder } from '@/hooks/use-create-salesforce-folder'
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Textarea } from '@/components/ui/textarea'
import { Folder } from 'lucide-react'
import { toast } from 'sonner'

interface CreateFolderDialogProps {
  open: boolean
  onOpenChange: (open: boolean) => void
  parentFolder: SalesforceFolder | null
  onFolderCreated: (folder: SalesforceFolder) => void
}

export const CreateFolderDialog: React.FC<CreateFolderDialogProps> = ({
  open,
  onOpenChange,
  parentFolder,
  onFolderCreated
}) => {
  const { createFolder, loading } = useCreateSalesforceFolder()
  const [formData, setFormData] = useState({
    name: '',
    description: ''
  })

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    
    if (!parentFolder || !formData.name.trim()) {
      return
    }

    try {
      const newFolder = await createFolder({
        name: formData.name.trim(),
        description: formData.description.trim(),
        parent_id: parentFolder.salesforce_id
      })

      toast.success('Carpeta creada amb èxit!', {
        description: `La carpeta "${formData.name}" s'ha creat correctament.`
      })

      onFolderCreated(newFolder)
      onOpenChange(false)
      setFormData({ name: '', description: '' })
    } catch (error) {
      console.error('Error creating folder:', error)
      const errorMessage = error instanceof Error ? error.message : 'Error desconegut al crear la carpeta'
      toast.error('Error al crear la carpeta', {
        description: errorMessage
      })
    }
  }

  const handleCancel = () => {
    onOpenChange(false)
    setFormData({ name: '', description: '' })
  }

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="sm:max-w-[425px]">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <Folder className="h-5 w-5" />
            Crear Nova Carpeta
          </DialogTitle>
          <DialogDescription>
            {parentFolder ? (
              <>
                Crear una nova carpeta dins de{' '}
                <span className="font-medium">"{parentFolder.name}"</span>
              </>
            ) : (
              'Selecciona una carpeta pare per crear una nova subcarpeta'
            )}
          </DialogDescription>
        </DialogHeader>

        <form onSubmit={handleSubmit} className="space-y-4">
          <div className="space-y-2">
            <Label htmlFor="folder-name">Nom de la carpeta *</Label>
            <Input
              id="folder-name"
              placeholder="Ex: Newsletter Gener 2024"
              value={formData.name}
              onChange={(e) => setFormData(prev => ({ ...prev, name: e.target.value }))}
              maxLength={100}
              required
            />
            <p className="text-xs text-muted-foreground">
              {formData.name.length}/100 caràcters
            </p>
          </div>

          <div className="space-y-2">
            <Label htmlFor="folder-description">Descripció</Label>
            <Textarea
              id="folder-description"
              placeholder="Descripció opcional de la carpeta..."
              value={formData.description}
              onChange={(e) => setFormData(prev => ({ ...prev, description: e.target.value }))}
              maxLength={255}
              rows={3}
            />
            <p className="text-xs text-muted-foreground">
              {formData.description.length}/255 caràcters
            </p>
          </div>

          <DialogFooter>
            <Button
              type="button"
              variant="outline"
              onClick={handleCancel}
              disabled={loading}
            >
              Cancel·lar
            </Button>
            <Button
              type="submit"
              disabled={!formData.name.trim() || !parentFolder || loading}
            >
              {loading ? (
                <>
                  <div className="mr-2 h-4 w-4 animate-spin rounded-full border-2 border-current border-t-transparent" />
                  Creant...
                </>
              ) : (
                'Crear Carpeta'
              )}
            </Button>
          </DialogFooter>
        </form>
      </DialogContent>
    </Dialog>
  )
}
