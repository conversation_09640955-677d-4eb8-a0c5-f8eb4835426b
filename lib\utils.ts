import { clsx, type ClassValue } from "clsx"
import { twMerge } from "tailwind-merge"

export function cn(...inputs: ClassValue[]) {
  return twMerge(clsx(inputs))
}

export function formatDate(dateString: string | undefined, showTime: boolean = true): string {
  if (dateString === undefined) return ''
  if (!dateString) return ''
  const date = new Date(dateString)
  if (isNaN(date.getTime())) return ''
  return date.toLocaleDateString('ca-ES') + (showTime ? " " +  date.toLocaleTimeString('ca-ES') : "")
}