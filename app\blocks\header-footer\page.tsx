"use client"

import React, { useState, useEffect } from "react"
import { useRouter } from "next/navigation"
import { useSession } from "next-auth/react"
import { NextPage } from 'next'
import { Plus, Search } from "lucide-react"

import { Button } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { HeadersFootersTable } from "@/components/table/headers-footers-table"
import { useHeadersFooters } from "@/hooks/use-headers-footers"
import { DEFAULT_PAGE_SIZE } from "@/constants/constants"

const Page: NextPage = () => {
  const router = useRouter()
  const { status } = useSession()
  const [headersFootersSearchQuery, setHeadersFootersSearchQuery] = useState("")
  const [debouncedHeadersFootersSearch, setDebouncedHeadersFootersSearch] = useState("")

  // Pagination state for headers/footers
  const [headersFootersPage, setHeadersFootersPage] = useState(1)
  const [headersFootersPageSize, setHeadersFootersPageSize] = useState(DEFAULT_PAGE_SIZE)

  // Debounce headers/footers search query
  useEffect(() => {
    const timer = setTimeout(() => {
      setDebouncedHeadersFootersSearch(headersFootersSearchQuery)
    }, 300)

    return () => clearTimeout(timer)
  }, [headersFootersSearchQuery])

  // Reset headers/footers page when search changes
  useEffect(() => {
    setHeadersFootersPage(1)
  }, [debouncedHeadersFootersSearch])

  // Fetch headers/footers with search
  const {
    headersFooters,
    loading: headersFootersLoading,
    error: headersFootersError,
    refetch: refetchHeadersFooters,
    totalCount: headersFootersTotalCount,
    hasNext: headersFootersHasNext,
    hasPrevious: headersFootersHasPrevious
  } = useHeadersFooters({
    search: debouncedHeadersFootersSearch || undefined,
    page: headersFootersPage,
    pageSize: headersFootersPageSize
  })

  const handleCreateHeaderFooter = () => {
    router.push('/blocks/header-footer/create')
  }

  // Show loading while session is being fetched
  if (status === "loading") {
    return (
      <div className="p-6">
        <div className="flex items-center justify-center h-32">
          <div className="text-muted-foreground">Carregant...</div>
        </div>
      </div>
    )
  }

  // Redirect to login if not authenticated
  if (status === "unauthenticated") {
    router.push('/login')
    return null
  }

  return (
    <div className="p-6 space-y-8">
      {/* Headers/Footers Section */}
      <div className="space-y-4">
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-3xl font-bold tracking-tight">Capçaleres i Peus de Pàgina</h1>
            <p className="text-muted-foreground">
              Gestiona les capçaleres i els peus de pàgina per a diferents marques i idiomes
            </p>
          </div>
          <Button onClick={handleCreateHeaderFooter}>
            <Plus className="mr-2 h-4 w-4" />
            Crear Capçalera/Peu
          </Button>
        </div>

        <div className="flex items-center space-x-4">
          <div className="relative flex-1 max-w-sm">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-muted-foreground w-4 h-4" />
            <Input
              placeholder="Cercar capçaleres/peus..."
              value={headersFootersSearchQuery}
              onChange={(e) => setHeadersFootersSearchQuery(e.target.value)}
              className="pl-10"
            />
          </div>
        </div>

        {headersFootersError && (
          <div className="p-4 text-sm text-red-600 bg-red-50 border border-red-200 rounded-md">
            Error: {headersFootersError}
          </div>
        )}

        <HeadersFootersTable
          data={headersFooters}
          loading={headersFootersLoading}
          onRefresh={refetchHeadersFooters}
          pagination={{
            page: headersFootersPage,
            pageSize: headersFootersPageSize,
            totalCount: headersFootersTotalCount,
            hasNext: headersFootersHasNext,
            hasPrevious: headersFootersHasPrevious,
            onPageChange: setHeadersFootersPage,
            onPageSizeChange: setHeadersFootersPageSize
          }}
        />
      </div>
    </div>
  )
}

export default Page