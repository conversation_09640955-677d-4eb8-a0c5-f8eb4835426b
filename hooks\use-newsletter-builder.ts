import { useState, useEffect, useCallback } from 'react'
import { useSession } from 'next-auth/react'
import { NewsletterBuilderData, UpdateNewsletterRequest } from '@/types/newsletter'
import { DEFAULT_API_URL } from '@/constants/constants'
import { extractApiErrorMessage } from '@/lib/api-error-utils'
import { toast } from 'sonner'

interface UseNewsletterBuilderParams {
  newsletterId: string
}

interface UseNewsletterBuilderReturn {
  newsletterData: NewsletterBuilderData | null
  loading: boolean
  error: string | null
  refetch: () => void
  updateNewsletter: (data: UpdateNewsletterRequest) => Promise<void>
  updating: boolean
  updateError: string | null
}

const API_BASE_URL = process.env.NEXT_PUBLIC_BACKEND_URL || DEFAULT_API_URL

export function useNewsletterBuilder({ newsletterId }: UseNewsletterBuilderParams): UseNewsletterBuilderReturn {
  const { data: session, status } = useSession()
  const [newsletterData, setNewsletterData] = useState<NewsletterBuilderData | null>(null)
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)
  const [updating, setUpdating] = useState(false)
  const [updateError, setUpdateError] = useState<string | null>(null)

  const getAuthHeaders = useCallback(() => {
    if (!session?.djangoAccessToken) {
      throw new Error('No authentication token available')
    }
    return {
      'Content-Type': 'application/json',
      'Authorization': `Bearer ${session.djangoAccessToken}`
    }
  }, [session?.djangoAccessToken])

  const fetchNewsletterData = useCallback(async () => {
    if (!newsletterId) {
      setError('Newsletter ID is required')
      setLoading(false)
      return
    }

    if (!session?.djangoAccessToken) {
      setError('Authentication required')
      setLoading(false)
      return
    }

    try {
      setLoading(true)
      setError(null)

      const headers = getAuthHeaders()
      const url = `${API_BASE_URL}/builder/newsletter-blocks?newsletter_parent_id=${newsletterId}`

      const response = await fetch(url, {
        method: 'GET',
        headers,
      })

      if (!response.ok) {
        const errorMessage = await extractApiErrorMessage(response, 'Failed to fetch newsletter data')
        throw new Error(errorMessage)
      }

      const data: NewsletterBuilderData = await response.json()
      setNewsletterData(data)
    } catch (err) {
      setError(err instanceof Error ? err.message : 'An error occurred')
      setNewsletterData(null)
    } finally {
      setLoading(false)
    }
  }, [newsletterId, session?.djangoAccessToken, getAuthHeaders])

  const updateNewsletter = useCallback(async (data: UpdateNewsletterRequest) => {
    if (!session?.djangoAccessToken) {
      throw new Error('Authentication required')
    }

    try {
      setUpdating(true)
      setUpdateError(null)

      // if the 

      const response = await fetch(`${API_BASE_URL}/builder/update-newsletter/`, {
        method: 'PUT',
        headers: getAuthHeaders(),
        body: JSON.stringify(data)
      })

      if (!response.ok) {
        const errorMessage = await extractApiErrorMessage(response, 'Failed to update newsletter')
        throw new Error(errorMessage)
      }

      // Refetch data after successful update
      await fetchNewsletterData()
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'An error occurred while updating newsletter'
      setUpdateError(errorMessage)
      toast.error(errorMessage)
      throw err
    } finally {
      setUpdating(false)
    }
  }, [session?.djangoAccessToken, getAuthHeaders, fetchNewsletterData])

  useEffect(() => {
    // Only fetch data when session is loaded and we have a token
    if (status === 'loading') {
      return // Wait for session to load
    }

    if (status === 'unauthenticated') {
      setError('Authentication required')
      setLoading(false)
      return
    }

    fetchNewsletterData()
  }, [fetchNewsletterData, status])

  return {
    newsletterData,
    loading,
    error,
    refetch: fetchNewsletterData,
    updateNewsletter,
    updating,
    updateError
  }
}
