"use client"

import { cn } from "@/lib/utils"
import { But<PERSON> } from "@/components/ui/button"
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card"
import { signIn, useSession, getProviders } from "next-auth/react"
import { useState, useEffect } from "react"

export function LoginForm({
  className,
  ...props
}: React.ComponentPropsWithoutRef<"div">) {
  const [isLoading, setIsLoading] = useState(false)
  const [debugInfo, setDebugInfo] = useState<any>({})
  const { data: session, status } = useSession()

  useEffect(() => {
    const loadDebugInfo = async () => {
      try {
        // Get available providers
        const providers = await getProviders()

        // Log comprehensive debug information
        const debug = {
          session,
          sessionStatus: status,
          providers,
          currentUrl: window.location.href,
          environment: {
            NODE_ENV: process.env.NODE_ENV,
            NEXTAUTH_URL: process.env.NEXTAUTH_URL,
          },
          timestamp: new Date().toISOString()
        }

        console.log('🔍 LOGIN DEBUG INFO:', debug)
        setDebugInfo(debug)
      } catch (error) {
        console.error('Error loading debug info:', error)
      }
    }

    loadDebugInfo()
  }, [session, status])

  const handleMicrosoftLogin = async () => {
    console.log('🚀 Starting Microsoft login...')
    setIsLoading(true)

    try {
      console.log('📋 Available providers:', debugInfo.providers)

      // Try to find Azure AD provider
      const azureProvider = debugInfo.providers?.['azure-ad'] || debugInfo.providers?.['azuread']
      console.log('🔍 Azure provider found:', azureProvider)

      if (!azureProvider) {
        console.error('❌ No Azure AD provider found in available providers')
        alert('Azure AD provider not configured. Check NextAuth setup.')
        return
      }

      console.log('🔄 Calling signIn with azure-ad...')
      const result = await signIn('azure-ad', {
        callbackUrl: '/',
        redirect: true
      })

      console.log('✅ SignIn result:', result)
      console.log('Token:', session?.accessToken)
    } catch (error) {
      console.error('❌ Login error:', error)
      alert(`Login failed: ${error}`)
    } finally {
      setIsLoading(false)
    }
  }

  return (
    <div className={cn("flex flex-col gap-6", className)} {...props}>
      <Card>
        <CardHeader>
          <CardTitle className="text-2xl">Inici de sessió</CardTitle>
          <CardDescription>
            Inicia sessió per accedir a la teva compta.
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="flex flex-col gap-6">
            <Button
              onClick={handleMicrosoftLogin}
              disabled={isLoading}
              className="w-full"
              size="lg"
            >
              {isLoading ? (
                <div className="flex items-center gap-2">
                  <div className="h-4 w-4 animate-spin rounded-full border-2 border-current border-t-transparent" />
                  Iniciant sessió...
                </div>
              ) : (
                <div className="flex items-center gap-2">
                  <svg className="h-5 w-5" viewBox="0 0 24 24">
                    <path
                      fill="currentColor"
                      d="M11.4 24H0V12.6h11.4V24zM24 24H12.6V12.6H24V24zM11.4 11.4H0V0h11.4v11.4zM24 11.4H12.6V0H24v11.4z"
                    />
                  </svg>
                  Inicia sessió amb Microsoft
                </div>
              )}
            </Button>

            {/* Debug Information */}
            {/* <div className="mt-6 p-4 bg-gray-50 rounded-lg text-xs">
              <h3 className="font-semibold mb-2">🔍 Informació de depuració</h3>
              <div className="space-y-2">
                <div>
                  <strong>Estat de la sessió:</strong> {status}
                </div>
                <div>
                  <strong>Sessió actual:</strong> {session ? "Sessió iniciada" : "No has iniciat sessió"}
                </div>
                <div>
                  <strong>Proveïdors disponibles:</strong>
                  {debugInfo.providers ? Object.keys(debugInfo.providers).join(', ') : "Carregant..."}
                </div>
                <div>
                  <strong>URL actual:</strong> {debugInfo.currentUrl}
                </div>
                <div>
                  <strong>Entorn:</strong> {process.env.NODE_ENV}
                </div>
                {session && (
                  <div>
                    <strong>Usuari:</strong> {session.user?.email || session.user?.name || "Desconegut"}
                  </div>
                )}
              </div>
              <details className="mt-2">
                <summary className="cursor-pointer font-semibold">Dades de depuració completes</summary>
                <pre className="mt-2 text-xs overflow-auto max-h-40 bg-white p-2 rounded border">
                  {JSON.stringify(debugInfo, null, 2)}
                </pre>
              </details>
            </div> */}
          </div>
        </CardContent>
      </Card>
    </div>
  )
}
